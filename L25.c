#include <fcntl.h>
#include <memory.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#define int int64_t // Global definition for 64-bit integers

int MAX_SIZE;

int *code,      // code segment
    *code_dump, // for dump
    *stack;     // stack segment
char *data;     // data segment (L<PERSON> uses this minimally, if at all)

int *pc, // pc register
    *sp, // rsp register
    *bp; // rbp register

int ax, // common register
    cycle;

// Global state for parser
char *src, *src_dump;
int *symbol_table, *symbol_ptr;
int token, token_val;
int line;

// For L25 parser and VM
int *entry_main_addr = 0;          // Entry point for the main block's code
int current_func_var_count = 0;    // Count of 'let' variables in current func/main for NVAR
int current_parsing_func_body = 0; // Flag: 1 if parsing func body (for return validation), 0 otherwise

// For parameter parsing in function definitions
#define MAX_PARAMS 10
int *param_sym_entries[MAX_PARAMS];

// For struct definitions
#define MAX_STRUCT_MEMBERS 20
int *struct_members[MAX_STRUCT_MEMBERS];
int *current_struct_def = 0;

// --- Enums ---

// Instruction set: adapted for L25
enum
{
    IMM,  // Load Immediate value into ax
    LEA,  // Load Effective Address (local/param offset from bp) into ax
    JMP,  // Jump to address
    JZ,   // Jump if ax is zero
    JNZ,  // Jump if ax is non-zero
    CALL, // Call function: push pc+1, jump to function address
    NVAR, // New Variables: allocate space on stack for local variables (bp
          // relative)
    DARG, // Discard Arguments: clean up arguments from stack after call
    RET,  // Return from function: restore bp, pc; expects return value in ax

    LI, // Load Integer from address in ax, into ax
    SI, // Store Integer from ax, into address pointed by stack top, then pop
        // address

    PUSH, // Push ax onto stack

    // Arithmetic Operations (LHS is from stack, RHS is ax, result in ax)
    ADD,
    SUB,
    MUL,
    DIV,

    // Comparison Operations (LHS is from stack, RHS is ax, result in ax: 1 for
    // true, 0 for false)
    EQ,
    NE,
    LT,
    GT,
    LE,
    GE,

    EXIT, // Terminate program with value from stack top

    // New Instructions for L25 I/O
    READ_INT_AX, // Reads an integer from input, stores it in ax
    PRINT_INT_AX, // Prints the integer value currently in ax

    // New Instructions for arrays and structs
    LEA_ARRAY,   // Load Effective Address for array element: ax = base_addr + index * element_size
    LEA_STRUCT   // Load Effective Address for struct member: ax = base_addr + member_offset
};

// L25 Token Types (keywords, symbols, operators)
enum
{
    Num = 128,
    Fun,
    Loc,
    Struct, // For struct types
    Array,  // For array types
    Id, // Basic classes and Id type

    // L25 Keywords
    Program,
    FuncKw,
    MainKw,
    Let,
    If,
    Else,
    Return,
    While,
    Input,
    Output,
    StructKw, // struct keyword

    // L25 Operators that need to be distinct tokens for the parser
    Assign,
    // Arithmetic and Comparison operators
    Add,
    Sub,
    Mul,
    Div, // For +, -, *, /
    Eq,
    Ne,
    Lt,
    Gt,
    Le,
    Ge, // For ==, !=, <, >, <=, >=

    // New tokens for arrays and structs
    Dot,    // . for struct member access
    Colon   // : for type annotations
};

// Symbol Table Entry Fields (for L25)
enum
{
    Token,  // Token type if keyword, or Id.
    Hash,   // Hash value of the identifier string
    Name,   // Pointer to the identifier string in the source code
    Class,  // Category: Fun, Loc, Struct, Array
    Type,   // Data type (INT_TYPE, STRUCT_TYPE, ARRAY_TYPE)
    Value,  // Fun: code address; Loc: stack offset from bp; Struct: struct definition; Array: array size
    Extra,  // For structs: member count; For arrays: element type
    SymSize // Number of fields in a symbol table entry
};

// Data Types (L25 types)
enum
{
    INT_TYPE,    // Representing L25's number type
    STRUCT_TYPE, // Representing struct types
    ARRAY_TYPE   // Representing array types
};

// --- Lexical Analyzer (Tokenizer) ---
void tokenize()
{
    char *ch_ptr;
    int current_hash_val; // Local variable for accumulating hash

    while ((token = *src++))
    {
        // 遇到换行符时，增加行号，跳过
        if (token == '\n')
        {
            line++;
        }
        //空格、制表符、回车符，跳过
        else if (token == ' ' || token == '\t' || token == '\r')
        {
        }
        // 字母和下划线，继续读取后续的字符，直到结束
        else if ((token >= 'a' && token <= 'z') || (token >= 'A' && token <= 'Z') || (token == '_'))
        {
            ch_ptr = src - 1;
            current_hash_val = token; // Initialize hash with the first character's value

            while ((*src >= 'a' && *src <= 'z') || (*src >= 'A' && *src <= 'Z') || (*src >= '0' && *src <= '9') ||
                   (*src == '_'))
            {
                current_hash_val = current_hash_val * 147 + *src++; // Hash calculation
            }
            current_hash_val = (current_hash_val << 6) + (src - ch_ptr); // Finalize hash

            symbol_ptr = symbol_table;
            while (symbol_ptr[Token])
            {
                if (current_hash_val == symbol_ptr[Hash] && !memcmp((char *)symbol_ptr[Name], ch_ptr, src - ch_ptr))
                {
                    token = symbol_ptr[Token];
                    return;
                }
                symbol_ptr = symbol_ptr + SymSize;
            }
            // Symbol not found, add new identifier
            symbol_ptr[Name] = (int64_t)ch_ptr; // Explicit cast for clarity if needed, Name stores address
            symbol_ptr[Hash] = current_hash_val;
            token = symbol_ptr[Token] = Id;
            return;
        }
        else if (token >= '0' && token <= '9')
        {
            token_val = token - '0';
            while (*src >= '0' && *src <= '9')
            {
                token_val = token_val * 10 + *src++ - '0';
            }
            token = Num;
            return;
        }
        else if (token == '/')
        {
            if (*src == '/')
            {
                while (*src != 0 && *src != '\n')
                    src++;
            }
            else
            {
                token = Div; // Use the enum value for Div
                return;
            }
        }
        else if (token == '=')
        {
            if (*src == '=')
            {
                src++;
                token = Eq;
            }
            else
            {
                token = Assign;
            } // Use enum for Eq and Assign
            return;
        }
        else if (token == '!')
        {
            if (*src == '=')
            {
                src++;
                token = Ne;
            } // Use enum for Ne
            else
            { /* '!' itself is not an L25 op, return ASCII; parser should handle
                 as error */
            }
            return;
        }
        else if (token == '<')
        {
            if (*src == '=')
            {
                src++;
                token = Le;
            }
            else
            {
                token = Lt;
            } // Use enum for Le and Lt
            return;
        }
        else if (token == '>')
        {
            if (*src == '=')
            {
                src++;
                token = Ge;
            }
            else
            {
                token = Gt;
            } // Use enum for Ge and Gt
            return;
        }
        else if (token == '+')
        {
            token = Add;
            return;
        } // Use enum for Add
        else if (token == '-')
        {
            token = Sub;
            return;
        } // Use enum for Sub
        else if (token == '*')
        {
            token = Mul;
            return;
        } // Use enum for Mul
        else if (token == '.')
        {
            token = Dot;
            return;
        } // Use enum for Dot
        else if (token == ':')
        {
            token = Colon;
            return;
        } // Use enum for Colon
        else if (token == '(' || token == ')' || token == '{' || token == '}' || token == ',' || token == ';' || token == '[' || token == ']')
        {
            return; // Return ASCII value for single-character punctuators
        }
        // Implicitly skip unknown characters by looping again
    }
}

// --- Parser Helper ---
void assert(int tk) // tk is int64_t due to #define
{
    if (token != tk)
    {
        char expected_char_printable = 0, current_char_printable = 0;
        // Check if tokens are within printable ASCII range for char display
        if (tk > 31 && tk < 127)
            expected_char_printable = (char)tk;
        if (token > 31 && token < 127)
            current_char_printable = (char)token;

        if (expected_char_printable && current_char_printable)
            printf("line %ld: error: expected token %ld ('%c'), but got %ld ('%c')\n", line, tk,
                   expected_char_printable, token, current_char_printable);
        else if (expected_char_printable)
            printf("line %ld: error: expected token %ld ('%c'), but got %ld\n", line, tk, expected_char_printable,
                   token);
        else if (current_char_printable)
            printf("line %ld: error: expected token %ld, but got %ld ('%c')\n", line, tk, token,
                   current_char_printable);
        else
            printf("line %ld: error: expected token %ld, but got %ld\n", line, tk, token);
        exit(-1);
    }
    tokenize();
}

// --- L25 Parser Functions ---
void parse_expr();
void parse_stmt();
void parse_stmt_list();
void parse_struct_def();
int find_struct_member(int *struct_sym, char *member_name);

void parse_factor()
{
    int *id_sym_entry; // This will be int64_t*
    if (token == Id)
    {
        id_sym_entry = symbol_ptr;
        tokenize();
        if (token == '(')
        {
            assert('(');
            int arg_count = 0; // This is int64_t
            if (token != ')')
            {
                while (1)
                {
                    parse_expr();
                    *++code = PUSH;
                    arg_count++;
                    if (token == ',')
                        assert(',');
                    else
                        break;
                }
            }
            assert(')');
            if (id_sym_entry[Class] != Fun)
            {
                printf("line %ld: error: '%s' is not a function.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            *++code = CALL;
            *++code = id_sym_entry[Value];
            if (arg_count > 0)
            {
                *++code = DARG;
                *++code = arg_count;
            }
        }
        else if (token == '[') // Array access
        {
            if (id_sym_entry[Class] != Loc || id_sym_entry[Type] != ARRAY_TYPE)
            {
                printf("line %ld: error: '%s' is not an array.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            assert('[');
            parse_expr(); // Index expression, result in ax
            assert(']');
            *++code = PUSH; // Push index onto stack
            *++code = LEA;
            *++code = id_sym_entry[Value]; // Load base address of array
            *++code = LEA_ARRAY; // Calculate element address
            *++code = LI; // Load value from calculated address
        }
        else if (token == Dot) // Struct member access
        {
            if (id_sym_entry[Class] != Loc || id_sym_entry[Type] != STRUCT_TYPE)
            {
                printf("line %ld: error: '%s' is not a struct.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            assert(Dot);
            assert(Id);
            int member_offset = find_struct_member(id_sym_entry, (char *)symbol_ptr[Name]);
            if (member_offset < 0)
            {
                printf("line %ld: error: struct member '%s' not found.\n", line, (char *)symbol_ptr[Name]);
                exit(-1);
            }
            *++code = LEA;
            *++code = id_sym_entry[Value]; // Load base address of struct
            *++code = LEA_STRUCT; // Calculate member address
            *++code = member_offset; // Member offset as operand
            *++code = LI; // Load value from calculated address
        }
        else
        {
            if (id_sym_entry[Class] != Loc)
            {
                printf("line %ld: error: undefined variable '%s'.\n", line, (char *)id_sym_entry[Name]);
                exit(-1);
            }
            *++code = LEA;
            *++code = id_sym_entry[Value];
            *++code = LI;
        }
    }
    else if (token == Num)
    {
        *++code = IMM;
        *++code = token_val;
        tokenize();
    }
    else if (token == '(')
    {
        assert('(');
        parse_expr();
        assert(')');
    }
    else
    {
        printf("line %ld: error: unexpected token in factor, got %ld.\n", line, token);
        exit(-1);
    }
}

void parse_term()
{
    parse_factor();
    while (token == Mul || token == Div) // Mul and Div are enum values
    {
        int op = token; // op is int64_t
        tokenize();
        *++code = PUSH;
        parse_factor();
        if (op == Mul)
            *++code = MUL;
        else
            *++code = DIV;
    }
}

void parse_sum_expr()
{
    int unary_op = 0; // int64_t
    if (token == Add)
    {
        unary_op = 1;
        tokenize();
    } // Add is enum value
    else if (token == Sub)
    {
        unary_op = 2;
        tokenize();
    } // Sub is enum value

    parse_term();

    if (unary_op == 2)
    {
        *++code = PUSH;
        *++code = IMM;
        *++code = -1;
        *++code = MUL;
    }

    while (token == Add || token == Sub) // Add and Sub are enum values
    {
        int op = token; // int64_t
        tokenize();
        *++code = PUSH;
        parse_term();
        if (op == Add)
            *++code = ADD;
        else
            *++code = SUB;
    }
}

void parse_expr()
{
    parse_sum_expr();
    // Eq, Ne, Lt, etc. are enum values
    if (token == Eq || token == Ne || token == Lt || token == Le || token == Gt || token == Ge)
    {
        int op = token; // int64_t
        tokenize();
        *++code = PUSH;
        parse_sum_expr();
        if (op == Eq)
            *++code = EQ;
        else if (op == Ne)
            *++code = NE;
        else if (op == Lt)
            *++code = LT;
        else if (op == Le)
            *++code = LE;
        else if (op == Gt)
            *++code = GT;
        else if (op == Ge)
            *++code = GE;
    }
}

void parse_declare_stmt()
{
    assert(Let);
    assert(Id);
    int *var_to_declare = symbol_ptr;

    // Refined duplicate check for local variables (Value is negative offset)
    if (var_to_declare[Class] == Loc && var_to_declare[Value] < 0)
    {
        printf("line %ld: error: duplicate declaration of local variable '%s'.\n", line, (char *)var_to_declare[Name]);
        exit(-1);
    }

    var_to_declare[Class] = Loc;

    // Check for type annotation
    if (token == Colon)
    {
        assert(Colon);
        assert(Id);
        // Look up the type in symbol table
        int *type_sym = symbol_ptr;
        if (type_sym[Class] == Struct)
        {
            var_to_declare[Type] = STRUCT_TYPE;
            var_to_declare[Extra] = (int64_t)type_sym; // Store pointer to struct definition
            current_func_var_count += type_sym[Extra]; // Allocate space for all struct members
        }
        else
        {
            printf("line %ld: error: unknown type '%s'.\n", line, (char *)type_sym[Name]);
            exit(-1);
        }
    }
    else if (token == '[') // Array declaration
    {
        assert('[');
        assert(Num);
        int array_size = token_val;
        assert(']');

        var_to_declare[Type] = ARRAY_TYPE;
        var_to_declare[Extra] = INT_TYPE; // Element type (only int for now)
        current_func_var_count += array_size; // Allocate space for all array elements
    }
    else
    {
        var_to_declare[Type] = INT_TYPE;
        current_func_var_count++;
    }

    var_to_declare[Value] = -current_func_var_count;

    if (token == Assign) // Assign is an enum value
    {
        assert(Assign);
        // Corrected code generation for 'let var = expr;'
        *++code = LEA;
        *++code = var_to_declare[Value]; // Load address of var_to_declare into ax
        *++code = PUSH;                  // Push the address onto the stack
        parse_expr();                    // Evaluate RHS, result is in ax
        *++code = SI;                    // Store ax (RHS value) into address popped from stack
    }
}

void parse_assign_stmt_body(int *id_var_entry)
{
    // Handle array element assignment: arr[index] = expr
    if (token == '[')
    {
        if (id_var_entry[Class] != Loc || id_var_entry[Type] != ARRAY_TYPE)
        {
            printf("line %ld: error: '%s' is not an array.\n", line, (char *)id_var_entry[Name]);
            exit(-1);
        }
        assert('[');
        parse_expr(); // Index expression, result in ax
        assert(']');
        assert(Assign);
        *++code = PUSH; // Push index onto stack
        *++code = LEA;
        *++code = id_var_entry[Value]; // Load base address of array
        *++code = LEA_ARRAY; // Calculate element address
        *++code = PUSH; // Push element address onto stack
        parse_expr(); // RHS expression, result in ax
        *++code = SI; // Store ax into address popped from stack
    }
    // Handle struct member assignment: struct.member = expr
    else if (token == Dot)
    {
        if (id_var_entry[Class] != Loc || id_var_entry[Type] != STRUCT_TYPE)
        {
            printf("line %ld: error: '%s' is not a struct.\n", line, (char *)id_var_entry[Name]);
            exit(-1);
        }
        assert(Dot);
        assert(Id);
        int member_offset = find_struct_member(id_var_entry, (char *)symbol_ptr[Name]);
        if (member_offset < 0)
        {
            printf("line %ld: error: struct member '%s' not found.\n", line, (char *)symbol_ptr[Name]);
            exit(-1);
        }
        assert(Assign);
        *++code = LEA;
        *++code = id_var_entry[Value]; // Load base address of struct
        *++code = LEA_STRUCT; // Calculate member address
        *++code = member_offset; // Member offset as operand
        *++code = PUSH; // Push member address onto stack
        parse_expr(); // RHS expression, result in ax
        *++code = SI; // Store ax into address popped from stack
    }
    // Handle simple variable assignment: var = expr
    else if (token == Assign)
    {
        assert(Assign); // Assign is an enum value
        if (id_var_entry[Class] != Loc)
        {
            printf("line %ld: error: cannot assign to non-variable or undeclared '%s'.\n", line,
                   (char *)id_var_entry[Name]);
            exit(-1);
        }
        *++code = LEA;
        *++code = id_var_entry[Value];
        *++code = PUSH;
        parse_expr();
        *++code = SI;
    }
    else
    {
        printf("line %ld: error: expected assignment operator after identifier.\n", line);
        exit(-1);
    }
}

void parse_func_call_stmt_body(int *func_id_entry)
{
    assert('(');
    int arg_count = 0; // int64_t
    if (token != ')')
    {
        while (1)
        {
            parse_expr();
            *++code = PUSH;
            arg_count++;
            if (token == ',')
                assert(',');
            else
                break;
        }
    }
    assert(')');
    if (func_id_entry[Class] != Fun)
    {
        printf("line %ld: error: '%s' is not a function.\n", line, (char *)func_id_entry[Name]);
        exit(-1);
    }
    *++code = CALL;
    *++code = func_id_entry[Value];
    if (arg_count > 0)
    {
        *++code = DARG;
        *++code = arg_count;
    }
}

void parse_if_stmt()
{
    assert(If);
    assert('(');
    parse_expr();
    assert(')');
    *++code = JZ;
    int *patch_jz = ++code; // patch_jz is int64_t*
    assert('{');
    parse_stmt_list();
    assert('}');
    if (token == Else)
    {
        assert(Else);
        *++code = JMP;
        int *patch_jmp_endif = ++code;   // int64_t*
        *patch_jz = (int64_t)(code + 1); // Cast to int64_t
        assert('{');
        parse_stmt_list();
        assert('}');
        *patch_jmp_endif = (int64_t)(code + 1); // Cast to int64_t
    }
    else
    {
        *patch_jz = (int64_t)(code + 1); // Cast to int64_t
    }
}

void parse_while_stmt()
{
    assert(While);
    int *loop_start = code + 1; // int64_t*
    assert('(');
    parse_expr();
    assert(')');
    *++code = JZ;
    int *patch_jz_end = ++code; // int64_t*
    assert('{');
    parse_stmt_list();
    assert('}');
    *++code = JMP;
    *++code = (int64_t)loop_start;       // Cast to int64_t
    *patch_jz_end = (int64_t)(code + 1); // Cast to int64_t
}

void parse_input_stmt()
{
    assert(Input);
    assert('(');
    int first = 1; // int64_t
    while (1)
    {
        if (!first)
            assert(',');
        first = 0;
        assert(Id);
        if (symbol_ptr[Class] != Loc)
        {
            printf("line %ld: error: 'input' target '%s' must be a local variable.\n", line, (char *)symbol_ptr[Name]);
            exit(-1);
        }
        // Corrected code generation for input
        *++code = LEA;
        *++code = symbol_ptr[Value]; // Load address of the variable into ax
        *++code = PUSH;              // Push the address onto the stack
        *++code = READ_INT_AX;       // Read input, result is in ax
        *++code = SI;                // Store ax (input value) into address popped from stack

        if (token != ',')
            break;
    }
    assert(')');
}

void parse_output_stmt()
{
    assert(Output);
    assert('(');
    int first = 1; // int64_t
    while (1)
    {
        if (!first)
            assert(',');
        first = 0;
        parse_expr();
        *++code = PRINT_INT_AX;
        if (token != ',')
            break;
    }
    assert(')');
}

void parse_stmt()
{
    if (token == Let)
        parse_declare_stmt();
    else if (token == Id)
    {
        int *id_entry = symbol_ptr; // int64_t*
        tokenize();
        if (token == Assign || token == '[' || token == Dot)
            parse_assign_stmt_body(id_entry); // Handle assignment, array, or struct access
        else if (token == '(')
            parse_func_call_stmt_body(id_entry);
        else
        {
            printf("line %ld: error: unexpected token '%ld' after identifier in "
                   "statement.\n",
                   line, token);
            exit(-1);
        }
    }
    else if (token == If)
        parse_if_stmt();
    else if (token == While)
        parse_while_stmt();
    else if (token == Input)
        parse_input_stmt();
    else if (token == Output)
        parse_output_stmt();
    else if (token == Return)
    {
        if (!current_parsing_func_body)
        {
            printf("line %ld: error: 'return' statement not allowed outside function "
                   "body.\n",
                   line);
            exit(-1);
        }
        // This case should ideally not be reached if parse_stmt_list stops before
        // 'Return'
        printf("line %ld: error: unexpected 'return' here, should be at end of "
               "func body.\n",
               line);
        exit(-1);
    }
    else
    {
        printf("line %ld: error: unknown statement starting with token %ld.\n", line, token);
        exit(-1);
    }
}

void parse_stmt_list()
{
    int stmt_count = 0; // int64_t
    while (token != '}' && !(current_parsing_func_body && token == Return) && token != 0)
    {
        int stmt_token = token; // Remember what kind of statement this is
        parse_stmt();

        // Only require semicolon for simple statements, not compound statements
        if (stmt_token != If && stmt_token != While)
        {
            assert(';');
        }
        stmt_count++;
    }
    if (stmt_count == 0 && !(current_parsing_func_body && token == Return))
    { // Allow empty if func body only has return
        printf("line %ld: error: statement list cannot be empty.\n", line);
        exit(-1);
    }
}

int parse_param_list() // Returns int64_t (param_count)
{
    int param_count = 0; // int64_t
    int first = 1;       // int64_t

    // Note: param_sym_entries is reset by the caller (parse_func_def)

    while (token != ')')
    {
        if (!first)
            assert(',');
        first = 0;
        assert(Id);

        if (param_count >= MAX_PARAMS)
        {
            printf("line %ld: error: too many parameters for function (max %d).\n", line, MAX_PARAMS);
            exit(-1);
        }

        // Rudimentary duplicate check within the current parameter list being parsed
        // This checks if the *exact same symbol table entry* for an Id is being re-added as a parameter.
        // A more robust check would compare names if multiple symbol table entries could exist for the same name in
        // different scopes.
        for (int k = 0; k < param_count; ++k)
        {
            if (param_sym_entries[k] == symbol_ptr)
            {
                printf("line %ld: error: duplicate parameter name '%s' in function definition.\n", line,
                       (char *)symbol_ptr[Name]);
                exit(-1);
            }
        }

        param_sym_entries[param_count] = symbol_ptr; // Store the current symbol_ptr (points to the Id in symbol_table)

        // Mark the Id entry in the main symbol_table as a Local variable of type INT
        symbol_ptr[Class] = Loc;
        symbol_ptr[Type] = INT_TYPE;
        // symbol_ptr[Value] (the stack offset) will be set after counting all params.

        param_count++;

        if (token != ',')
            break;
    }

    // Now assign the correct stack offsets.
    // Parameters are pushed onto the stack such that the first argument in the call
    // is at a higher memory address (further from bp after frame setup) than the last argument.
    // If func(a,b,c) is called, stack (relative to new bp after NVAR) is:
    // bp+4: value of a
    // bp+3: value of b
    // bp+2: value of c
    // param_sym_entries[i] is the symbol table entry for the (i+1)-th parameter in definition order.
    // e.g., for func(a,b,c):
    // i=0 is 'a', should get offset bp + (param_count-0+1) = bp+4 (if param_count=3)
    // i=1 is 'b', should get offset bp + (param_count-1+1) = bp+3
    // i=2 is 'c', should get offset bp + (param_count-2+1) = bp+2
    for (int i = 0; i < param_count; ++i)
    {
        param_sym_entries[i][Value] = (param_count - i) + 1;
    }
    return param_count;
}

void parse_func_def()
{
    // Reset temporary parameter storage for this function
    for (int i = 0; i < MAX_PARAMS; ++i)
        param_sym_entries[i] = NULL;

    assert(FuncKw);
    assert(Id);
    if (symbol_ptr[Class] == Fun)
    {
        printf("line %ld: error: duplicate function definition '%s'.\n", line, (char *)symbol_ptr[Name]);
        exit(-1);
    }
    symbol_ptr[Class] = Fun;
    symbol_ptr[Type] = INT_TYPE;
    symbol_ptr[Value] = (int64_t)(code + 1); // Cast to int64_t

    current_func_var_count = 0;

    assert('(');
    if (token != ')')
        parse_param_list();
    assert(')');

    assert('{');
    current_parsing_func_body = 1;

    *++code = NVAR;
    int *nvar_patch_addr = ++code; // int64_t*
    *nvar_patch_addr = 0;          // Initialize to 0, will be patched

    parse_stmt_list();

    *nvar_patch_addr = current_func_var_count;

    assert(Return);
    parse_expr();
    assert(';');
    *++code = RET;

    assert('}');
    current_parsing_func_body = 0;
}

void parse_struct_def()
{
    assert(StructKw);
    assert(Id);

    if (symbol_ptr[Class] == Struct)
    {
        printf("line %ld: error: duplicate struct definition '%s'.\n", line, (char *)symbol_ptr[Name]);
        exit(-1);
    }

    int *struct_sym = symbol_ptr;
    struct_sym[Class] = Struct;
    struct_sym[Type] = STRUCT_TYPE;
    current_struct_def = struct_sym;

    // Reset struct members array
    for (int i = 0; i < MAX_STRUCT_MEMBERS; ++i)
        struct_members[i] = NULL;

    assert('{');

    int member_count = 0;
    while (token != '}' && token != 0)
    {
        assert(Id);

        if (member_count >= MAX_STRUCT_MEMBERS)
        {
            printf("line %ld: error: too many members in struct (max %d).\n", line, MAX_STRUCT_MEMBERS);
            exit(-1);
        }

        // Check for duplicate member names
        for (int k = 0; k < member_count; ++k)
        {
            if (struct_members[k] == symbol_ptr)
            {
                printf("line %ld: error: duplicate member name '%s' in struct definition.\n", line,
                       (char *)symbol_ptr[Name]);
                exit(-1);
            }
        }

        struct_members[member_count] = symbol_ptr;
        symbol_ptr[Class] = Loc; // Mark as local for now
        symbol_ptr[Type] = INT_TYPE; // Only int members for now
        symbol_ptr[Value] = member_count; // Store member offset

        member_count++;
        assert(';');
    }

    assert('}');

    if (member_count == 0)
    {
        printf("line %ld: error: struct cannot be empty.\n", line);
        exit(-1);
    }

    struct_sym[Extra] = member_count; // Store member count
    struct_sym[Value] = (int64_t)struct_members; // Store pointer to members array (simplified)
}

int find_struct_member(int *struct_var_sym, char *member_name)
{
    if (struct_var_sym[Type] != STRUCT_TYPE)
        return -1;

    int *struct_def_sym = (int *)struct_var_sym[Extra];
    if (!struct_def_sym)
        return -1;

    int member_count = struct_def_sym[Extra];

    // Search through the struct members
    for (int i = 0; i < member_count; ++i)
    {
        if (struct_members[i] && strcmp((char *)struct_members[i][Name], member_name) == 0)
        {
            return i; // Return member offset
        }
    }

    return -1; // Member not found
}

void parse()
{
    line = 1;
    token = 1;
    tokenize();

    assert(Program);
    assert(Id);
    assert('{');

    // Parse struct definitions and function definitions
    while (token == StructKw || token == FuncKw)
    {
        if (token == StructKw)
            parse_struct_def();
        else
            parse_func_def();
    }

    assert(MainKw);
    entry_main_addr = code + 1; // int64_t*
    current_func_var_count = 0;

    assert('{');
    current_parsing_func_body = 0;

    *++code = NVAR;
    int *main_nvar_patch_addr = ++code; // int64_t*
    *main_nvar_patch_addr = 0;

    parse_stmt_list();

    *main_nvar_patch_addr = current_func_var_count;

    assert('}');

    assert('}');

    *++code = IMM;
    *++code = 0;
    *++code = PUSH; //
    *++code = EXIT;

    if (token != 0)
    {
        printf("line %ld: error: unexpected tokens after end of program.\n", line);
        exit(-1);
    }
}

// --- Keyword Setup ---
void keyword()
{
    int i; // int64_t
    char *keywords_str[] = {"program", "func", "main", "let", "if", "else", "return", "while", "input", "output", "struct", NULL};
    // Ensure these enum values are correct and match L25TokenTypes enum
    int keyword_tokens[] = {Program, FuncKw, MainKw, Let, If, Else, Return, While, Input, Output, StructKw};

    char *saved_src = src;

    for (i = 0; keywords_str[i]; ++i)
    {
        src = keywords_str[i];
        tokenize();
        symbol_ptr[Token] = keyword_tokens[i];
    }
    src = saved_src;
}

// --- Virtual Machine Initialization ---
// This function returns standard int, so undefine int64_t temporarily
#ifdef int
#undef int
#endif
int init_vm()
{
#define int int64_t // Redefine for internal use
    //分配代码段内存: 使用 malloc(MAX_SIZE) 为 代码段 (code) 分配内存。代码段将用来存储由语法分析器 (parse())
    //生成的虚拟机指令（字节码）。同时，它也将这个内存地址赋给了 code_dump
    //指针，这个指针主要用于后续可能的调试输出（比如 write_as() 函数会用到它来读取完整的代码段）。
    if (!(code = code_dump = malloc(MAX_SIZE)))
    {
        printf("malloc failed for code segment\n");
        return -1;
    } // Return standard int
    if (!(data = malloc(MAX_SIZE)))
    {
        printf("malloc failed for data segment\n");
        return -1;
    }
    if (!(stack = malloc(MAX_SIZE)))
    {
        printf("malloc failed for stack segment\n");
        return -1;
    }
    if (!(symbol_table = malloc(MAX_SIZE / 8)))
    {
        printf("malloc failed for symbol_table\n");
        return -1;
    }

    memset(code, 0, MAX_SIZE);
    memset(data, 0, MAX_SIZE);
    memset(stack, 0, MAX_SIZE);
    memset(symbol_table, 0, MAX_SIZE / 8);
    return 0; // Return standard int
#undef int    // Clean up local define
}
#define int                                                                                                            \
    int64_t // Restore global define if needed (though typically not after last
            // function)

// --- Virtual Machine Execution ---
// This function returns int64_t because of global define. We'll cast its result
// in main.
int run_vm(int argc_param, char **argv_param)
{
    int op;                                       // This is int64_t
    bp = sp = (int *)((int64_t)stack + MAX_SIZE); // Cast stack to int64_t before pointer arithmetic

    if (!entry_main_addr)
    {
        printf("L25 main block entry point not defined (parser error or no main "
               "block).\n");
        exit(-1);
    }
    pc = entry_main_addr;

    cycle = 0;
    while (1)
    {
        cycle++;
        op = *pc++;

        if (op == IMM)
            ax = *pc++;
        else if (op == LEA)
            ax = (int64_t)(bp + *pc++); // bp is int64_t*, *pc++ is offset
        else if (op == JMP)
            pc = (int *)*pc; // pc is int64_t*
        else if (op == JZ)
            pc = ax ? pc + 1 : (int *)*pc;
        else if (op == JNZ)
            pc = ax ? (int *)*pc : pc + 1;
        else if (op == CALL)
        {
            *--sp = (int64_t)(pc + 1);
            pc = (int *)*pc;
        } // Cast to int64_t
        else if (op == NVAR)
        {
            *--sp = (int64_t)bp;
            bp = sp;
            sp = sp - *pc++;
        } // Cast to int64_t
        else if (op == DARG)
            sp = sp + *pc++;
        else if (op == RET)
        {
            sp = bp;
            bp = (int *)*sp++;
            pc = (int *)*sp++;
        }
        else if (op == LI)
            ax = *(int *)ax; // ax is int64_t, *(int*)ax dereferences int64_t*
        else if (op == SI)
            *(int *)*sp++ = ax;
        else if (op == PUSH)
            *--sp = ax;
        else if (op == ADD)
            ax = *sp++ + ax;
        else if (op == SUB)
            ax = *sp++ - ax;
        else if (op == MUL)
            ax = *sp++ * ax;
        else if (op == DIV)
        {
            if (ax == 0)
            {
                fprintf(stderr, "Runtime Error: Division by zero (cycle: %ld).\n", cycle);
                exit(-2);
            }
            ax = *sp++ / ax;
        }
        else if (op == EQ)
            ax = (*sp++ == ax);
        else if (op == NE)
            ax = (*sp++ != ax);
        else if (op == LT)
            ax = (*sp++ < ax);
        else if (op == GT)
            ax = (*sp++ > ax);
        else if (op == LE)
            ax = (*sp++ <= ax);
        else if (op == GE)
            ax = (*sp++ >= ax);
        else if (op == EXIT)
        {
            printf("L25 program exited with code: %ld (cycles: %ld)\n", *sp, cycle);
            return *sp;
        }
        else if (op == READ_INT_AX)
        {
            fflush(stdout);
            if (scanf("%ld", &ax) != 1)
            { // ax is int64_t
                fprintf(stderr,
                        "Runtime Error: Failed to read an integer from input (cycle: "
                        "%ld).\n",
                        cycle);
                ax = 0;
            }
        }
        else if (op == PRINT_INT_AX)
        {
            printf("%ld\n", ax); // ax is int64_t
            fflush(stdout);
        }
        else if (op == LEA_ARRAY)
        {
            // ax has base address, stack top has index
            int index = *sp++;
            ax = ax + index * sizeof(int64_t); // Calculate element address
        }
        else if (op == LEA_STRUCT)
        {
            // ax has base address, next instruction has member offset
            int offset = *pc++;
            ax = ax + offset * sizeof(int64_t); // Calculate member address
        }
        else
        {
            printf("Runtime Error: Unknown instruction %ld at pc=%p (cycle: %ld)\n", op, (void *)(pc - 1), cycle);
            return -1;
        }
    }
    return 0;
}

// --- Debug: Write Assembled Instructions (Optional) ---
void write_as()
{
    // No need for local #define int int64_t if global one is active
    int fd; // int64_t
    char buffer[100];
    char *insts = "IMM ,LEA ,JMP ,JZ  ,JNZ ,CALL,NVAR,DARG,RET ,LI  ,SI  ,PUSH,"
                  "ADD ,SUB ,MUL ,DIV ,EQ  ,NE  ,LT  ,GT  ,LE  ,GE  ,EXIT,"
                  "RDAX,PTAX,LEAR,LEAS,";

    if ((fd = open("assemble.l25", O_WRONLY | O_CREAT | O_TRUNC, 0644)) < 0)
    {
        printf("Failed to open assemble.l25 for writing.\n");
        return;
    }

    int *current_code_ptr = code_dump; // int64_t*
    int instruction_idx = 0;           // int64_t

    while (current_code_ptr < code)
    {
        instruction_idx++;
        int current_op = *current_code_ptr; // int64_t

        char mnemonic_buffer[6];
        if (current_op >= 0 && (current_op * 5 + 4) < strlen(insts))
        {
            strncpy(mnemonic_buffer, insts + (current_op * 5), 4);
            mnemonic_buffer[4] = '\0';
        }
        else
        {
            strcpy(mnemonic_buffer, "UNKN");
        }

        sprintf(buffer, "%04ld %-4s", instruction_idx, mnemonic_buffer);
        write(fd, buffer, strlen(buffer));

        current_code_ptr++;

        if (current_op == IMM || current_op == LEA || current_op == JZ || current_op == JNZ || current_op == CALL ||
            current_op == NVAR || current_op == DARG || current_op == JMP || current_op == LEA_STRUCT)
        {
            if (current_code_ptr < code)
            {
                sprintf(buffer, " %ld\n", *current_code_ptr);
                current_code_ptr++;
            }
            else
            {
                strcpy(buffer, " (missing operand)\n");
            }
        }
        else
        {
            strcpy(buffer, "\n");
        }
        write(fd, buffer, strlen(buffer));
    }
    close(fd);
    printf("Assembly-like output written to assemble.l25\n");
}

// --- Source Code Loading ---
// This function returns standard int, so undefine int64_t temporarily
#ifdef int
#undef int
#endif
int load_src(char *file)
{
#define int int64_t // Redefine for internal use
    int fd;         // int64_t
    int cnt;        // int64_t
    // 如果文件不存在或无法打开，它会打印错误信息并返回 -1（表示失败）
    if ((fd = open(file, O_RDONLY)) < 0)
    {
        printf("Error: Could not open source file '%s'\n", file);
        return -1; // Standard int
    }
    // 如果内存分配失败（比如系统内存不足），它会打印错误信息，关闭之前打开的文件，并返回 -1
    // src 指针将在后续的词法分析中被移动，而 src_dump 则保留了原始的起始地址，以便之后可以释放这块内存
    if (!(src = src_dump = malloc(MAX_SIZE)))
    {
        printf("Error: Could not malloc for source code buffer\n");
        close(fd);
        return -1; // Standard int
    }
    // 使用 read()
    // 函数将打开的文件的内容读取到刚刚分配的内存缓冲区中。它会记录实际读取的字节数。如果读取失败或文件是空的（读取字节数为
    // 0 或负数），它会打印错误信息，释放之前分配的内存，关闭文件，并返回 -1
    if ((cnt = read(fd, src, MAX_SIZE - 1)) <= 0)
    {
        printf("Error: Could not read source code from '%s' or file is empty\n", file);
        free(src_dump);
        src = src_dump = NULL;
        close(fd);
        return -1; // Standard int
    }
    // 添加字符串结束符: 读取成功后，它会在读取到的内容的末尾添加一个空字符
    // \0。这非常重要，因为它将内存中的数据转换成了一个标准的 C 语言字符串，这样后续处理字符串的函数（比如 tokenize()
    // 中的 *src++）才能正确工作，知道在哪里停止。
    src[cnt] = 0;
    close(fd);
    // 如果所有步骤都顺利完成，函数返回 0，表示源代码已成功加载到内存中。
    return 0; // Standard int
#undef int    // Clean up local define
}
#define int int64_t // Restore global define

// --- Main Program Entry (Host) ---
#ifdef int
#undef int // Undefine before main signature
#endif
int main(int argc, char *argv[])
{
#define int int64_t // Redefine int to int64_t for the scope of main and calls from main

    if (argc < 2)
    {
        printf("Usage: %s <sourcefile.l25>\n", argv[0]);
        return -1;
    }

    MAX_SIZE = 256 * 1024 * 8; // 代码段、数据段、栈等内存区域的大小

    // Calls to functions that now manage their own 'int' definition or expect
    // standard int
    //读取用户指定的 L25 源文件内容到内存中
    if (load_src(argv[1]) != 0)
        return -1;
    //为虚拟机的各个部分（如代码区、栈区、符号表）分配所需的内存空间
    if (init_vm() != 0)
        return -1;

    // These functions use the global #define int int64_t
    keyword(); // L25 语言的关键字预先加载到符号表中，以便后续的词法分析器能识别它们
    parse();   //启动语法分析过程。这个过程会读取源代码，检查语法是否正确，并将 L25
               //代码转换成虚拟机可以理解的指令（字节码）

    write_as(); // Uses global #define int int64_t

    // run_vm returns int64_t due to global define; cast to standard int for
    // main's return
    // 调用 run_vm() 函数，让虚拟机开始执行刚刚由 parse() 生成的指令
    return (int)run_vm(argc - 1, argv + 1);
    //返回退出码: main 函数最后会返回 run_vm() 函数的执行结果（通常是 L25 程序通过 EXIT
    //指令指定的退出码，或者是一个错误代码），这个返回值会传递给操作系统。
}

// No need to #undef int at the very end of the file if main is the last
// function that needs the standard signature and there are no further standard
// library interactions that would be confused by 'int' being 'int64_t'.